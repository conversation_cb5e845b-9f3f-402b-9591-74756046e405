/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

/* Header */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Search Section */
.search-section {
    padding: 25px 30px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.search-container input {
    flex: 1;
    padding: 12px 45px 12px 45px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-container input:focus {
    border-color: #4facfe;
}

.search-icon {
    position: absolute;
    left: 15px;
    color: #999;
    z-index: 1;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    color: #666;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: #4facfe;
    color: white;
    border-color: #4facfe;
}

.template-count {
    color: #666;
    font-size: 14px;
    text-align: center;
}

/* Filter Options */
.filter-options {
    display: none;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-options.show {
    display: flex;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 500;
    color: #333;
    min-width: 70px;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    outline: none;
    cursor: pointer;
}

/* Template Sections */
.recommended-section,
.all-templates-section {
    padding: 25px 30px;
}

.recommended-section {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-bottom: 1px solid #e0e0e0;
}

.recommended-section h2,
.all-templates-section h2 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.recommended-section h2 {
    color: #8b4513;
}

/* Template Grid */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.recommended-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Template Cards */
.template-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: #4facfe;
}

.template-card.selected {
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
}

.template-card.recommended {
    border-color: #ff6b6b;
    background: linear-gradient(135deg, #fff 0%, #fff8f0 100%);
}

.template-card.recommended:hover {
    border-color: #ff5252;
}

.template-preview {
    height: 150px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.preview-content {
    text-align: center;
    padding: 20px;
    width: 100%;
}

.preview-content h3 {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
    letter-spacing: 1px;
}

.preview-text {
    font-size: 12px;
    color: #666;
    margin-bottom: 15px;
}

.preview-border {
    height: 3px;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    margin: 0 auto;
    width: 80%;
}

.preview-border.classic {
    background: linear-gradient(90deg, #8b4513, #d2691e);
    height: 4px;
}

.preview-border.elegant {
    background: linear-gradient(90deg, #9c27b0, #e91e63);
    height: 2px;
    width: 60%;
}

.preview-border.simple {
    background: #666;
    height: 1px;
    width: 50%;
}

.template-info {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.template-info h4 {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.template-tag {
    background: #ff6b6b;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    padding: 25px 30px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        max-height: 95vh;
    }

    .header {
        padding: 15px 20px;
    }

    .header h1 {
        font-size: 20px;
    }

    .search-section,
    .recommended-section,
    .all-templates-section,
    .action-buttons {
        padding: 20px;
    }

    .template-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }

    .recommended-grid {
        grid-template-columns: 1fr;
    }

    .filter-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Animation for template cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.template-card {
    animation: fadeInUp 0.5s ease forwards;
}

/* Hidden class for filtering */
.hidden {
    display: none !important;
}