// Global variables
let selectedTemplate = null;
let allTemplates = [];
let filteredTemplates = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTemplates();
    updateTemplateCount();
});

// Initialize templates array
function initializeTemplates() {
    allTemplates = Array.from(document.querySelectorAll('.template-card'));
    filteredTemplates = [...allTemplates];
    
    // Add click event listeners to template cards
    allTemplates.forEach(card => {
        card.addEventListener('click', function() {
            selectTemplateCard(this);
        });
    });
}

// Search functionality
function searchTemplates() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const styleFilter = document.getElementById('styleFilter').value;
    
    filteredTemplates = allTemplates.filter(template => {
        const templateText = template.textContent.toLowerCase();
        const category = template.dataset.category;
        const style = template.dataset.style;
        
        const matchesSearch = templateText.includes(searchTerm);
        const matchesCategory = !categoryFilter || category === categoryFilter;
        const matchesStyle = !styleFilter || style === styleFilter;
        
        return matchesSearch && matchesCategory && matchesStyle;
    });
    
    updateTemplateDisplay();
    updateTemplateCount();
}

// Filter templates
function filterTemplates() {
    searchTemplates(); // Reuse search logic with filters
}

// Toggle filter options
function toggleFilters() {
    const filterOptions = document.getElementById('filterOptions');
    filterOptions.classList.toggle('show');
}

// Update template display based on filters
function updateTemplateDisplay() {
    allTemplates.forEach(template => {
        if (filteredTemplates.includes(template)) {
            template.classList.remove('hidden');
        } else {
            template.classList.add('hidden');
        }
    });
}

// Update template count
function updateTemplateCount() {
    const count = filteredTemplates.length;
    document.getElementById('templateCount').textContent = count;
}

// Select template card
function selectTemplateCard(card) {
    // Remove previous selection
    if (selectedTemplate) {
        selectedTemplate.classList.remove('selected');
    }
    
    // Add selection to clicked card
    card.classList.add('selected');
    selectedTemplate = card;
    
    // Enable select button
    document.getElementById('selectBtn').disabled = false;
    
    // Add visual feedback
    card.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Select template action
function selectTemplate() {
    if (selectedTemplate) {
        const templateName = selectedTemplate.querySelector('.template-info h4').textContent;
        const templateCategory = selectedTemplate.dataset.category;
        const templateStyle = selectedTemplate.dataset.style;
        
        // Show confirmation
        showNotification(`Selected: ${templateName}`, 'success');
        
        // Here you would typically send the selection to the parent application
        console.log('Template selected:', {
            name: templateName,
            category: templateCategory,
            style: templateStyle,
            element: selectedTemplate
        });
        
        // Simulate closing the dialog after a short delay
        setTimeout(() => {
            closeDialog();
        }, 1500);
    }
}

// Cancel selection
function cancelSelection() {
    if (confirm('Are you sure you want to cancel template selection?')) {
        closeDialog();
    }
}

// Close dialog
function closeDialog() {
    // Add closing animation
    document.querySelector('.container').style.transform = 'scale(0.95)';
    document.querySelector('.container').style.opacity = '0';
    
    setTimeout(() => {
        // Here you would typically close the modal or navigate back
        console.log('Dialog closed');
        // For demo purposes, we'll just reload the page
        window.location.reload();
    }, 300);
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4caf50' : '#2196f3'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 500;
        animation: slideInRight 0.3s ease;
    `;
    
    // Add animation keyframes
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // ESC to close
    if (e.key === 'Escape') {
        closeDialog();
    }
    
    // Enter to select (if template is selected)
    if (e.key === 'Enter' && selectedTemplate) {
        selectTemplate();
    }
    
    // Focus search on Ctrl+F or Cmd+F
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        document.getElementById('searchInput').focus();
    }
});

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Auto-focus search input
setTimeout(() => {
    document.getElementById('searchInput').focus();
}, 500);
